import React, {
  useState,
  forwardRef,
  useImperative<PERSON>andle,
  useEffect,
  useMemo,
  useRef,
  ForwardRefRenderFunction,
  useCallback
} from "react";
import { Modal } from "bootstrap";
import { toast } from "react-toastify";
import axios from "axios";
import Select from "react-select";
import { AgGridReact } from "ag-grid-react";
import { ColDef } from "ag-grid-community";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-quartz.css";
import {
  getEmployeeList,
  getClubEmpDetails,
  insertClubData,
} from "./TripHistoryRequests";
import { Employee, ClubEmployeeData, LocationData } from "./TripHistoryModels";
import ReactSelect from "../../../../Helper/ReactSelect";
import { SingleValue } from "react-select";
import { LoadingOverlay, NoRowsOverlay } from "../../../../Helper/AgGridOverlays";
import { useNavigate } from "react-router-dom";

interface Option {
  value: string | number;
  label: string;
}

export interface EmployeeListModalRef {
  openModal: () => void;
  closeModal: () => void;
}

interface EmployeeListModalProps {
  id: string;
  title: string;
  rosterId: string;
  loginDateTime?: string;
  tripType?: string;
}

interface EmployeeOption {
  value: string;
  label: string;
  mobile: string;
}

interface LocationOption {
  value: number;
  label: string;
}

const EmployeeListModal: ForwardRefRenderFunction<EmployeeListModalRef, EmployeeListModalProps> = (props, ref) => {
  const [show, setShow] = useState(false);
  const [loading, setLoading] = useState(false);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [selectedEmployee, setSelectedEmployee] =
    useState<EmployeeOption | null>(null);
  const [loadingClubDetails, setLoadingClubDetails] = useState(false);
  const [gridData, setGridData] = useState<ClubEmployeeData[]>([]);
  const [selectedLocation, setSelectedLocation] =
    useState<LocationOption | null>(null);
  const [locationOptions, setLocationOptions] = useState<LocationOption[]>([]);
  const [employeeData, setEmployeeData] = useState<ClubEmployeeData | null>(
    null
  );
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [routeId, setRouteId] = useState<string>("");
  const [submittingEmployeeId, setSubmittingEmployeeId] = useState<string | null>(null);
  const gridRef = useRef<AgGridReact>(null);
  const modalRef = useRef<HTMLDivElement>(null);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [networkType, setNetworkType] = useState<string | null>(null);
  const [lastSubmitPayload, setLastSubmitPayload] = useState<any>(null);
  const navigate = useNavigate();

  const handleClose = () => {
    const modalElement = document.getElementById(props.id);
    if (modalElement) {
      const modalInstance = Modal.getInstance(modalElement);
      if (modalInstance) {
        modalInstance.hide();
      }
    }
    // Reset all states
    setShow(false);
    setSelectedEmployee(null);
    setGridData([]);
    setEmployees([]);
    setLoading(false);
    setLoadingClubDetails(false);
    setLocationOptions([]);
    setSelectedLocation(null);
    setEmployeeData(null);
    setErrorMessage("");
    setSubmittingEmployeeId(null);
  };

  const openModal = () => {
    const modalElement = document.getElementById(props.id);
    if (modalElement) {
      const modalInstance = Modal.getInstance(modalElement);
      if (modalInstance) {
        modalInstance.show();
      } else {
        new Modal(modalElement).show();
      }
    }
    setShow(true);
  };

  useImperativeHandle(ref, () => ({
    openModal,
    closeModal: handleClose
  }));

  // Fetch data when modal is opened and rosterId changes
  useEffect(() => {
    if (show && props.rosterId) {
      fetchEmployeeList();
    }
  }, [show, props.rosterId]);

  const fetchEmployeeList = async () => {
    if (!props.rosterId) return;

    try {
      setLoading(true);
      setError(null);
      setNetworkType("getEmployeeList");
      const response = await getEmployeeList({ roster_id: props.rosterId });

      if (response.data.success) {
        setEmployees(response.data.employees);
      } else {
        // Business logic error: show inline
        setErrorMessage("Failed to fetch employee list");
        // Do not set error (no overlay)
      }
    } catch (error) {
      let errorMessage = "Failed to fetch employee list";
      if (axios.isAxiosError(error)) {
        const statusCode = error.response?.status;
        if (statusCode === 403 || statusCode === 401) {
          errorMessage = "Your session has expired. Please log in again.";
        }
      }
      setError(errorMessage); // Only network/API errors trigger overlay
      setErrorMessage("");
      toast.error(errorMessage);
      console.error("Error fetching employee list:", error);
    } finally {
      setLoading(false);
    }
  };

  const LocationCellRenderer = useCallback((props: any) => {
    const [selectedValue, setSelectedValue] = useState<Option | null>(null);
    const selectRef = useRef<any>(null);

    useEffect(() => {
      if (props.data) {
        setSelectedValue({
          value: props.data.LOCATION_ID,
          label: props.data.LOCATION_NAME,
        });
      }
    }, [props.data]);

    const customStyles = {
      control: (provided: any) => ({
        ...provided,
        backgroundColor: 'white',
        border: '1px solid #ddd',
        borderRadius: '4px',
        boxShadow: 'none',
        '&:hover': {
          border: '1px solid #ddd',
        }
      }),
      menu: (provided: any) => ({
        ...provided,
        zIndex: 9999,
        marginTop: '2px',
        width: '300px',
        position: 'absolute',
        overflow: 'hidden'
      }),
      menuList: (provided: any) => ({
        ...provided,
        maxHeight: locationOptions.length > 0 ? '200px' : '0',
        overflowY: locationOptions.length > 0 ? 'auto' : 'hidden',
        padding: 0
      }),
      menuPortal: (base: any) => ({
        ...base,
        zIndex: 9999,
      }),
      container: (provided: any) => ({
        ...provided,
        width: '100%',
      }),
      valueContainer: (provided: any) => ({
        ...provided,
        height: '40px',
        padding: '0 8px',
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center'
      }),
      input: (provided: any) => ({
        ...provided,
        margin: '0',
        padding: '0'
      }),
      placeholder: (provided: any) => ({
        ...provided,
        margin: '0',
        position: 'static'
      }),
      option: (provided: any, state: any) => ({
        ...provided,
        backgroundColor: state.isSelected ? '#0d6efd' : state.isFocused ? '#e9ecef' : 'white',
        color: state.isSelected ? 'white' : '#495057',
        padding: '8px 12px',
        cursor: 'pointer',
        '&:active': {
          backgroundColor: '#0d6efd',
        },
      }),
    }

    const handleLocationChange = (selected: Option | null) => {
      setSelectedValue(selected);
      if (selected) {
        try {
          const updatedData = {
            ...props.data,
            LOCATION_ID: selected.value,
            LOCATION_NAME: selected.label,
          };

          setGridData((prevGridData) => {
            const newGridData = prevGridData.map((row) =>
              row.EMPLOYEES_ID === props.data.EMPLOYEES_ID ? updatedData : row
            );
            return newGridData;
          });

          if (props.api) {
            props.api.applyTransaction({
              update: [updatedData],
            });

            props.api.refreshCells({
              force: true,
              rowNodes: [props.node],
              columns: ["LOCATION_NAME"],
            });
          }
        } catch (error) {
          console.error("Error updating location:", error);
          toast.error("Failed to update location");
        }
      }
    };

    // Get locationOptions from the context
    const locationOptions = props.context?.locationOptions || [];

    return (
      <div>
        <Select
          ref={selectRef}
          value={selectedValue}
          options={locationOptions}
          onChange={handleLocationChange}
          styles={customStyles}
          placeholder="Select Location"
          menuPortalTarget={document.body}
          menuPosition={'fixed'}
          maxMenuHeight={200}
          isClearable={true}
        />
      </div>
    );
  }, [setGridData]);

  // Custom function to format date and time
  const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  const handleSubmit = async (data: ClubEmployeeData) => {
    try {
      setSubmittingEmployeeId(data.EMPLOYEES_ID);
      // Get the latest data from the grid API using the ref
      const gridApi = gridRef.current?.api;
      if (!gridApi) {
        toast.error("Grid API not found");
        return;
      }
      const rowNode = gridApi.getRowNode(data.EMPLOYEES_ID);
      if (!rowNode) {
        toast.error("Employee data not found");
        return;
      }
      const currentRowData = rowNode.data;
      if (!currentRowData.LOCATION_ID) {
        toast.error("Please select a location first");
        return;
      }
      // Use the most recent data from the grid
      const finalData = {
        empid: currentRowData.EMPLOYEES_ID,
        location_id: currentRowData.LOCATION_ID.toString(),
        roster_id: props.rosterId,
        mobile: "9543417214",
        logindatetime: props.loginDateTime || "",
        pickdroptime: formatDate(new Date()),
        asset_movement: "test",
      };
      const response = await insertClubData(finalData);
      if (response.data.success) {
        toast.success(response.data.club_status);
        handleClose();
      } else {
        setErrorMessage("Failed to add employee clubbing");
        toast.error("Failed to add employee clubbing");
      }
    } catch (error) {
      console.error("Error in handleSubmit:", error);
      setError("An error occurred while processing the submission"); // Only network/API errors trigger overlay
      setErrorMessage("");
      toast.error("An error occurred while processing the submission");
    } finally {
      setSubmittingEmployeeId(null);
      // Force refresh the grid to update the button state
      if (gridRef.current?.api) {
        gridRef.current.api.refreshCells({
          force: true,
          columns: ['action']
        });
      }
    }
  };

  const ActionCellRenderer = useCallback((params: any) => {
    const isSubmitting = submittingEmployeeId === params.data.EMPLOYEES_ID;

    const handleClick = async () => {
      const api = params.api;
      const rowNode = api.getRowNode(params.data.EMPLOYEES_ID);
      if (rowNode) {
        await handleSubmit(rowNode.data);
      }
    };

    return (
      <button
        className="btn btn-primary btn-sm"
        onClick={handleClick}
        disabled={isSubmitting}
      >
        {isSubmitting ? (
          <span 
            className="spinner-border spinner-border-sm" 
            role="status" 
            aria-hidden="true"
          />
        ) : (
          'Submit'
        )}
      </button>
    );
  }, [submittingEmployeeId, handleSubmit]);

  const columnDefs = useMemo<ColDef[]>(() => [
    { 
      headerName: "Name",
      field: "EMPNAME",
      sortable: true,
      filter: 'agTextColumnFilter',
      filterParams: {
        filterOptions: ['contains'],
        maxNumConditions: 1
      },
      minWidth: 150,
      width: 150,
      resizable: true,
      suppressSizeToFit: true
    },
    { 
      headerName: "Email",
      field: "EMAIL",
      sortable: true,
      filter: 'agTextColumnFilter',
      filterParams: {
        filterOptions: ['contains'],
        maxNumConditions: 1
      },
      minWidth: 200,
      width: 200,
      resizable: true,
      suppressSizeToFit: true
    },
    { 
      headerName: "Gender",
      field: "GENDER",
      sortable: true,
      filter: 'agTextColumnFilter',
      filterParams: {
        filterOptions: ['contains'],
        maxNumConditions: 1
      },
      minWidth: 100,
      width: 100,
      resizable: true,
      suppressSizeToFit: true
    },
    { 
      headerName: "Employee Id",
      field: "EMPLOYEES_ID",
      sortable: true,
      filter: 'agTextColumnFilter',
      filterParams: {
        filterOptions: ['contains'],
        maxNumConditions: 1
      },
      minWidth: 120,
      width: 120,
      resizable: true,
      suppressSizeToFit: true
    },
    { 
      headerName: "Project Name",
      field: "PROJECT_NAME",
      sortable: true,
      filter: 'agTextColumnFilter',
      filterParams: {
        filterOptions: ['contains'],
        maxNumConditions: 1
      },
      minWidth: 150,
      width: 150,
      resizable: true,
      suppressSizeToFit: true
    },
    { 
      headerName: "Address",
      field: "ADDRESS",
      sortable: true,
      filter: 'agTextColumnFilter',
      filterParams: {
        filterOptions: ['contains'],
        maxNumConditions: 1
      },
      minWidth: 250,
      width: 250,
      resizable: true,
      suppressSizeToFit: true
    },
    { 
      headerName: "Pickup Area",
      field: "LOCATION_NAME",
      sortable: true,
      filter: false,
      minWidth: 320,
      width: 320,
      resizable: true,
      suppressSizeToFit: true,
      cellRenderer: LocationCellRenderer,
      // cellStyle: { padding: '5px' },
      cellRendererParams: {
        context: { locationOptions }
      }
    },
    { 
      headerName: "Pickup/Drop Time",
      field: "pickup_time",
      sortable: true,
      filter: 'agTextColumnFilter',
      filterParams: {
        filterOptions: ['contains'],
        maxNumConditions: 1
      },
      minWidth: 180,
      width: 180,
      resizable: true,
      valueGetter: () => {
        return formatDate(new Date());
      }
    },
    {
      headerName: "Action",
      field: "action",
      sortable: false,
      filter: false,
      minWidth: 120,
      width: 120,
      cellRenderer: ActionCellRenderer
    }
  ], [LocationCellRenderer, ActionCellRenderer, locationOptions, submittingEmployeeId]);

  const defaultColDef = useMemo(() => ({
    flex: 1,
    minWidth: 100,
    filter: 'agTextColumnFilter',
    filterParams: {
      filterOptions: ['contains'],
      maxNumConditions: 1
    },
    floatingFilter: true,
    sortable: true,
    resizable: true,
  }), []);

  const handleGoClick = async () => {
    if (!selectedEmployee) {
      toast.error("Please select an employee first");
      return;
    }

    if (!props.loginDateTime || !props.tripType) {
      toast.error("Login time or trip type not available");
      return;
    }

    try {
      setLoadingClubDetails(true);
      setErrorMessage("");
      setLocationOptions([]); // Clear locations immediately
      setGridData([]); // Clear grid data immediately
      setIsDataLoaded(false); // Reset data loaded state
      setError(null); // Reset error state
      setNetworkType("getClubEmpDetails"); // Set network type for retry

      const response = await getClubEmpDetails({
        emp_id: selectedEmployee.value,
        login_datetime: props.loginDateTime,
        trip_type: props.tripType,
      });

      if (response.data.success) {
        if (!response.data.employees || response.data.employees.length === 0) {
          setErrorMessage("No employee data found");
          return;
        }

        if (response.data.status === 5 && response.data.employees?.[0]?.msg) {
          setErrorMessage(response.data.employees[0].msg);
          return;
        }

        // Transform location_lists into options for ReactSelect
        const locationsList = response.data.locations
          ?.filter((loc: LocationData) => loc.ACTIVE === "1")
          ?.map((loc: LocationData) => ({
            value: loc.LOCATION_ID,
            label: loc.LOCATION_NAME,
          }))
          ?.sort((a, b) => a.label.localeCompare(b.label)) || [];

        if (locationsList.length === 0) {
          setErrorMessage("No active locations available");
          return;
        }

        setLocationOptions(locationsList);

        if (response.data.employees?.length > 0) {
          const employeeData = response.data.employees.map(
            (emp: ClubEmployeeData) => {
              const matchingLocation = locationsList.find(
                (loc) => loc.value === emp.LOCATION_ID
              );
              return {
                ...emp,
                LOCATION_ID: matchingLocation?.value || emp.LOCATION_ID,
                LOCATION_NAME: matchingLocation?.label || "",
              };
            }
          );

          setGridData(employeeData);
          setIsDataLoaded(true);
          setError(null);
        }
      } else {
        setErrorMessage("Failed to fetch club employee details");
      }
    } catch (error) {
      console.error("Error fetching club employee details:", error);
      let errorMessage = "Failed to fetch club employee details";
      if (axios.isAxiosError(error)) {
        const statusCode = error.response?.status;
        if (statusCode === 403 || statusCode === 401) {
          errorMessage = "Your session has expired. Please log in again.";
        }
      }
      setError(errorMessage); // Only network/API errors trigger overlay
      setErrorMessage("");
      toast.error(errorMessage);
    } finally {
      setLoadingClubDetails(false);
    }
  };

  const handleTryAgainClick = () => {
    setError(null);
    switch (networkType) {
      case "getClubEmpDetails":
        handleGoClick();
        break;
      case "getEmployeeList":
        fetchEmployeeList();
        break;
      default:
        console.error("Unknown network type");
    }
  };

  const employeeOptions: EmployeeOption[] = employees.map((emp) => ({
    value: emp.EMPLOYEES_ID,
    label: `${emp.EMPLOYEES_ID} - ${emp.MOBILE}`,
    mobile: emp.MOBILE,
  }));

  const handleEmployeeChange = (selectedOption: SingleValue<Option>) => {
    const employeeOption = selectedOption as EmployeeOption | null;
    setSelectedEmployee(employeeOption);
  };

  useEffect(() => {
    const modalElement = document.getElementById(props.id);

    const handleModalHidden = () => {
      handleClose();
    };

    if (modalElement) {
      modalElement.addEventListener('hidden.bs.modal', handleModalHidden);
    }

    return () => {
      if (modalElement) {
        modalElement.removeEventListener('hidden.bs.modal', handleModalHidden);
      }
    };
  }, [props.id]);

  const handleReclubClick = (e: React.MouseEvent) => {
    e.preventDefault();
    handleClose(); 
    navigate('/transport/data/reclub');
  };

  const renderErrorOverlay = () => (
    <div className="d-flex flex-column justify-content-center align-items-center h-100 w-100 position-absolute top-0 start-0 bg-white bg-opacity-75" style={{zIndex: 10, minHeight: 400}}>
      <div className="text-center mb-4">
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </div>
      <div>
        <button className="btn btn-danger" onClick={handleTryAgainClick} disabled={!!submittingEmployeeId || loading || loadingClubDetails}>
          {(submittingEmployeeId || loading || loadingClubDetails) ? (
            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
          ) : null}
          Try Again
        </button>
      </div>
    </div>
  );

  return (
    <div 
      className="modal fade" 
      style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
      tabIndex={-1} 
      id={props.id}
      ref={modalRef}
    >
      <div className="modal-dialog modal-xl" style={{ maxWidth: '1200px' }}>
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">{props.title}</h5>
            <button
              type="button"
              className="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            />
          </div>
          <div className="modal-body" style={{ height: '700px', padding: '1.5rem', position: 'relative' }}>
            <div className="container-fluid h-100">
              {/* Error Overlay for any network error */}
              {error && renderErrorOverlay()}
              <div className={`row mb-4 ${error ? 'pointer-events-none opacity-50' : ''}`}>
                <div className="col-md-6 offset-md-3">
                  <div className="card">
                    <div className="card-body">
                      <div className="mb-3">
                        <div className="d-flex align-items-center">
                          <label
                            className="me-3 text-nowrap fw-bold"
                            style={{ color: "#dc3545", minWidth: "120px" }}
                          >
                            Mobile Number :
                          </label>
                          <div className="flex-grow-1">
                            <ReactSelect
                              options={employeeOptions}
                              value={selectedEmployee}
                              onChange={handleEmployeeChange}
                              placeholder="Search Employee ID or Mobile"
                              disabled={!!error}
                            />
                          </div>
                          <div className="ms-2">
                            <button
                              className="btn btn-primary"
                              onClick={handleGoClick}
                              disabled={loadingClubDetails || !selectedEmployee || !!error}
                              style={{
                                minWidth: "60px",
                                height: "40px",
                                padding: "0.5rem 1rem",
                                fontSize: "0.925rem",
                                borderRadius: "0.475rem",
                                fontWeight: 500,
                                lineHeight: "1.5",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center"
                              }}
                            >
                              {loadingClubDetails ? (
                                <span
                                  className="spinner-border spinner-border-sm"
                                  role="status"
                                  aria-hidden="true"
                                />
                              ) : (
                                "Go"
                              )}
                            </button>
                          </div>
                        </div>
                        {errorMessage && (
                          <div className="mt-8 d-flex justify-content-center align-items-center">
                            <span
                              style={{
                                color: "#dc3545",
                                fontSize: "1.10rem",
                                textAlign: "center",
                              }}
                            >
                              {(() => {
                                const msg = errorMessage;
                                const reclubIndex = msg.indexOf("Reclub");
                                if (reclubIndex !== -1) {
                                  return (
                                    <>
                                      {msg.substring(0, reclubIndex)}
                                      <a
                                        href="#"
                                        style={{ color: "#0d6efd", textDecoration: "underline", marginLeft: 4 }}
                                        onClick={handleReclubClick}
                                      >
                                        Reclub
                                      </a>
                                      {msg.substring(reclubIndex + "Reclub".length)}
                                    </>
                                  );
                                }
                                return msg;
                              })()}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className={`row flex-grow-1 ${error ? 'pointer-events-none opacity-50' : ''}`}>
                <div className="col">
                  <div className="card-body p-0">
                    {isDataLoaded && gridData.length > 0 && (
                      <div
                        className="ag-theme-quartz"
                        style={{
                          height: "500px",
                          width: "100%"
                        }}
                      >
                        <AgGridReact
                          ref={gridRef}
                          rowData={gridData}
                          columnDefs={columnDefs}
                          defaultColDef={defaultColDef}
                          pagination={true}
                          paginationPageSize={10}
                          rowHeight={45}
                          headerHeight={45}
                          animateRows={false}
                          enableCellTextSelection={true}
                          suppressRowHoverHighlight={false}
                          suppressScrollOnNewData={true}
                          noRowsOverlayComponent={NoRowsOverlay}
                          loadingOverlayComponent={LoadingOverlay}
                          getRowId={(params) => params.data.EMPLOYEES_ID}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default forwardRef(EmployeeListModal);
